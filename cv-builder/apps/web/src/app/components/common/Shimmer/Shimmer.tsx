import { cn } from '@/lib/utils';

interface ShimmerProps {
  className?: string;
  children?: React.ReactNode;
  isActive?: boolean;
}

export function Shimmer({
  className,
  children,
  isActive = true,
}: ShimmerProps) {
  if (!isActive) {
    return <>{children}</>;
  }

  return (
    <div className={cn('relative overflow-hidden', className)}>
      {children}
      <div className="absolute inset-0 pointer-events-none">
        {/* First shimmer wave */}
        <div
          className="absolute inset-0 shimmer-animation -skew-x-12"
          style={{
            background:
              'linear-gradient(304.43deg, rgba(255, 255, 255, 0) 14.9%, rgba(196, 156, 245, 0.2) 37.44%, rgba(255, 255, 255, 0.2) 64%, rgba(196, 156, 245, 0.2) 80.1%, rgba(255, 255, 255, 0.2) 98.61%)',
          }}
        />
        {/* Second shimmer wave - starts when first is halfway */}
        <div
          className="absolute inset-0 shimmer-animation -skew-x-12"
          style={{
            background:
              'linear-gradient(304.43deg, rgba(255, 255, 255, 0) 14.9%, rgba(196, 156, 245, 0.2) 37.44%, rgba(255, 255, 255, 0.2) 64%, rgba(196, 156, 245, 0.2) 80.1%, rgba(255, 255, 255, 0.2) 98.61%)',
            animationDelay: '-0.75s',
          }}
        />
      </div>
    </div>
  );
}
