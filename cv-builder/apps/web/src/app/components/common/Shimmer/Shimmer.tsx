import { cn } from '@/lib/utils';

interface ShimmerProps {
  className?: string;
  children?: React.ReactNode;
  isActive?: boolean;
}

export function Shimmer({ className, children, isActive = true }: ShimmerProps) {
  if (!isActive) {
    return <>{children}</>;
  }

  return (
    <div className={cn('relative overflow-hidden', className)}>
      {children}
      <div className="absolute inset-0 pointer-events-none">
        <div
          className={cn(
            'absolute inset-0 shimmer-animation',
            'bg-gradient-to-r from-transparent via-msWhite/40 to-transparent',
            '-skew-x-12'
          )}
        />
      </div>
    </div>
  );
}
